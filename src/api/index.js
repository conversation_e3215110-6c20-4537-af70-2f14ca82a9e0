import request from '@/utils/request'

// 用户相关API
export const userApi = {
  // 用户登录
  login(data) {
    return request.post('/user/login', data)
  },

  // 获取用户信息
  getUserInfo() {
    return request.get('/user/info')
  },

  // 用户登出
  logout() {
    return request.post('/user/logout')
  },

  // 修改密码
  changePassword(data) {
    return request.put('/user/password', data)
  },

  // 更新用户信息
  updateUserInfo(data) {
    return request.put('/user/info', data)
  }
}

// 仪表盘相关API
export const dashboardApi = {
  // 获取仪表盘统计数据
  getStats() {
    return request.get('/dashboard/stats')
  },

  // 获取图表数据
  getChartData(params) {
    return request.get('/dashboard/chart', params)
  },

  // 获取最近活动
  getRecentActivity() {
    return request.get('/dashboard/activity')
  }
}

// 数据管理相关API
export const dataApi = {
  // 获取数据列表
  getDataList(params) {
    return request.get('/data/list', params)
  },

  // 创建数据
  createData(data) {
    return request.post('/data', data)
  },

  // 更新数据
  updateData(id, data) {
    return request.put(`/data/${id}`, data)
  },

  // 删除数据
  deleteData(id) {
    return request.delete(`/data/${id}`)
  },

  // 批量删除数据
  batchDeleteData(ids) {
    return request.delete('/data/batch', { ids })
  },

  // 导出数据
  exportData(params) {
    return request.download('/data/export', params)
  },

  // 导入数据
  importData(formData) {
    return request.upload('/data/import', formData)
  }
}

// 文件相关API
export const fileApi = {
  // 上传文件
  uploadFile(formData, config = {}) {
    return request.upload('/file/upload', formData, {
      onUploadProgress: progressEvent => {
        if (config.onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          )
          config.onProgress(percentCompleted)
        }
      },
      ...config
    })
  },

  // 删除文件
  deleteFile(fileId) {
    return request.delete(`/file/${fileId}`)
  },

  // 获取文件列表
  getFileList(params) {
    return request.get('/file/list', params)
  },

  // 下载文件
  downloadFile(fileId, fileName) {
    return request
      .download(
        `/file/download/${fileId}`,
        {},
        {
          loading: false // 下载时不显示全局loading
        }
      )
      .then(response => {
        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      })
  }
}

// 系统配置相关API
export const systemApi = {
  // 获取系统配置
  getSystemConfig() {
    return request.get('/system/config')
  },

  // 更新系统配置
  updateSystemConfig(data) {
    return request.put('/system/config', data)
  },

  // 获取系统日志
  getSystemLogs(params) {
    return request.get('/system/logs', params)
  },

  // 清理系统缓存
  clearCache() {
    return request.post('/system/cache/clear')
  }
}

// 场景相关API
export const projectStructureApi = {
  getStructureTree(params) {
    return request.get('/api/Project/Structure/GetStructureTree', params)
  }
}

// 导出所有API
export default {
  userApi,
  dashboardApi,
  dataApi,
  fileApi,
  systemApi,
  projectStructureApi
}
