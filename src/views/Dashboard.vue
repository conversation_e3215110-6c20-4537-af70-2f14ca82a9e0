<template>
  <div class="dashboard">
    <div class="header">
      <img class="logo" src="../assets/images/icons/icon.png" alt="" />
      <span class="title">环北部湾广东水资源配置工程 — BIM管理平台</span>
      <div class="tab">
        <div
          class="item"
          :class="isProjectSelected ? 'selected' : ''"
          @click="handleClickTab"
        >
          <span>项目一览</span>
          <img
            src="../assets/images/common/selected.png"
            alt=""
            v-if="isProjectSelected"
          />
        </div>
        <div
          class="item"
          :class="isProjectSelected ? '' : 'selected'"
          @click="handleClickTab"
        >
          <span>模型平台</span>
          <img
            src="../assets/images/common/selected.png"
            alt=""
            v-if="!isProjectSelected"
          />
        </div>
      </div>
    </div>
    <div class="container">
      <div class="left">
        <div class="top">
          <span>环北部湾广东水资源配置项目</span>
        </div>
        <div class="line"></div>
        <div class="tree">
          <el-tree
            class="el-tree-cus"
            node-key="BusinessCode"
            ref="elTree"
            empty-text="暂无子级"
            :highlight-current="true"
            :default-expand-all="true"
            :data="treeData"
            :props="elTreeProps"
            @node-click="onElTreeNodeClick"
          >
            <div class="el-tree-node-cus" slot-scope="{ node, data }">
              <el-tooltip
                effect="dark"
                :content="node.label"
                placement="top"
                :enterable="false"
              >
                <div class="label">{{ node.label }}</div>
              </el-tooltip>
              <div class="extra" @click.stop="onExtraBtnClick(node, $event)">
                <div class="extra-btn icon-interface-list"></div>
                <div class="extra-label">{{ data.length }}</div>
              </div>
            </div>
          </el-tree>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'Dashboard',
  components: {},
  data() {
    return {
      isProjectSelected: true,
      treeData: [],
      elTreeProps: {
        children: 'Children',
        label: 'MenuName'
      }
    }
  },
  computed: {
    ...mapGetters(['getDashboardData']),
    dashboardData() {
      return this.getDashboardData
    }
  },
  methods: {
    /**
     * 获取结构树
     * @returns {Promise<void>}
     */
    async getTree() {
      let params = {
        Token: window.getToken(),
        parentId: '0',
        organizeId: window.getProjectId()
      }
      const res = await this.$api.projectStructureApi.getStructureTree(params)
      if (res.Ret === 1) {
        this.treeData = res.Data
      }
    },
    // 处理树节点点击事件
    onElTreeNodeClick(data, node) {
      console.log('onElTreeNodeClick', data, node)
      this.clickTreeMenuID = data.Id
    },
    handleClickTab() {
      this.isProjectSelected = !this.isProjectSelected
    },
    ...mapActions(['fetchDashboardData']),
    refreshData() {
      this.$message({
        message: '正在刷新数据...',
        type: 'info'
      })
      this.fetchDashboardData()
      setTimeout(() => {
        this.$message({
          message: '数据刷新成功！',
          type: 'success'
        })
      }, 1000)
    }
  },
  mounted() {
    this.getTree()
  }
}
</script>

<style lang="scss" scoped>
// 变量定义
$icon-size: 60px;
$transition-duration: 0.3s;
$white: white;
$text-color: #2c3e50;
$label-color: #7f8c8d;
$button-min-width: 120px;

.dashboard {
  display: flex;
  flex-direction: column;
  background: #2c3e50;
  height: 100vh;
  width: 100%;
  .header {
    display: flex;
    align-items: center;
    background-size: cover;
    background-repeat: no-repeat;
    width: 100%;
    height: 46px;
    background-image: url('../assets/images/background/bg.png');
    .logo {
      width: 30px;
      height: 30px;
      margin-left: 20px;
    }
    .title {
      margin-left: 7px;
      font-weight: 500;
      font-size: 24px;
      color: #ffffff;
      line-height: 33px;
      text-align: left;
      font-style: normal;
    }
    .tab {
      margin-left: 100px;
      display: flex;
      flex: 1;
      height: 100%;
      .item {
        margin-top: 17px;
        position: relative;
        margin-right: 60px;
        cursor: pointer;
        span {
          margin-top: 60px;
          font-size: 16px;
          color: white;
        }
        &.selected {
          span {
            color: #19ffff;
          }
        }
        img {
          position: absolute;
          left: -18px;
          bottom: 0;
          width: 104px;
          height: 14px;
        }
      }
    }
  }
  .container {
    background: #242b33;
    height: calc(100% - 46px);
    width: 100%;
    .left {
      margin: 10px;
      width: 300px;
      height: calc(100% - 20px);
      background: linear-gradient(180deg, #162d59 0%, #081733 100%);
      border-radius: 8px;
      .top {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 40px;
        width: 100%;
        span {
          font-weight: 500;
          font-size: 14px;
          color: #ffffff;
          line-height: 20px;
          text-align: left;
          font-style: normal;
        }
      }
      .line {
        width: 100%;
        height: 1px;
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }
}
</style>
