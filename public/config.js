// 环境配置
window.CONFIG = {
  // 开发环境配置
  development: {
    baseURL: 'http://**************:3011',
    timeout: 15000,
    uploadURL: 'http://localhost:3000/upload',
    websocketURL: 'ws://localhost:3001',
    // 功能开关
    debug: true,
    mock: true,
    enablePWA: false,
    enableAnalytics: false,
    logLevel: 'debug'
  },

  // 生产环境配置
  production: {
    baseURL: '/api',
    timeout: 10000,
    uploadURL: '/api/upload',
    websocketURL: 'wss://your-domain.com/ws',
    // 功能开关
    debug: false,
    mock: false,
    enablePWA: true,
    enableAnalytics: true,
    logLevel: 'error'
  },

  // 测试环境配置
  test: {
    baseURL: 'http://test-api.example.com/api',
    timeout: 10000,
    uploadURL: 'http://test-api.example.com/upload',
    websocketURL: 'ws://test-api.example.com:3001',
    // 功能开关
    debug: true,
    mock: false,
    enablePWA: false,
    enableAnalytics: false,
    logLevel: 'info'
  }
}

// 获取当前环境配置
window.getConfig = function () {
  const env = process.env.NODE_ENV || 'development'
  return window.CONFIG[env] || window.CONFIG.development
}
window.getProjectId = function () {
  return '7d46ab2f-0433-4853-9312-76be2ad3b8bd'
}
window.getToken = function () {
  return 'D5318751F71E499BB6D6495D88DBEF27D22C22E9A9F57E3BDDD9153C5AE8D3775B8B0CB49A8CDD9BD6D449895D6EB3F2209BEB1A6157503631684BCDFEB15B497C5963E96DA1D18D37369383EBB8128A'
}
